#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: prompt.py
@time: 2025/2/14 15:24
@Description:
"""
from typing import Optional, Dict

from langfuse import Langfuse
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder

from config.config_center import settings
from common.myLog import logger
from const.enums import PromptType
from common.cache_manager import global_cache

logger.name = __name__


class PromptManager:
    """提示模板管理类"""

    _cache_manager = global_cache
    
    def __init__(self):
        """初始化提示模板管理器"""
        # 初始化Langfuse客户端
        self._init_langfuse()
        
        # 默认系统消息
        self._default_system_message = "You are a helpful assistant"
        
        # 内部缓存
        self._cache: Dict[str, str] = {}
        
        # 从配置中加载默认提示模板
        self._load_default_prompts()
        
    def _init_langfuse(self) -> None:
        """初始化Langfuse客户端，并处理可能的异常"""
        try:
            self.langfuse = Langfuse()
        except Exception as e:
            logger.error(f"Langfuse初始化失败: {str(e)}")
            self.langfuse = None

    def _load_default_prompts(self) -> None:
        """从配置中加载默认提示模板"""
        try:
            self.prompts = {
                PromptType.RAG.value: settings.prompt.rag,
                PromptType.ROUTER.value: settings.prompt.router,
                PromptType.SEARCH.value: settings.prompt.search,
                PromptType.REQUIREMENT.value: settings.prompt.requirement,
                PromptType.TECHNICAL.value: settings.prompt.technical,
                PromptType.GENERAL.value: settings.prompt.general,
                PromptType.CHAT.value: settings.prompt.chat,
                PromptType.TESTCASE.value: settings.prompt.testcase,
                PromptType.API.value: settings.prompt.api_prompt,
                PromptType.DEMAND_ANALYSIS.value: settings.prompt.demand_analysis
            }
        except AttributeError as e:
            logger.error(f"加载默认提示模板失败: {str(e)}")
            self.prompts = {}

    def history_prompt(
        self,
        name: Optional[str] = None,
        system_message: Optional[str] = None,
        human_message: Optional[str] = None,
    ) -> ChatPromptTemplate:
        return ChatPromptTemplate.from_messages([
            ("system", system_message or self._default_system_message),
            MessagesPlaceholder(variable_name="history"),
            ("human", human_message or self.get_prompt(name) if name else "{question}")
        ])

    def chat_template(
        self,
        name: Optional[str] = None,
        system_message: Optional[str] = None,
        human_message: Optional[str] = None,
    ) -> ChatPromptTemplate:
        return ChatPromptTemplate.from_messages([
            ("system", self.get_prompt(name) if name else system_message or self._default_system_message),
            ("human", human_message or "{question}")
        ])

    def utils_prompt(
        self,
        name: Optional[str] = None
    ) -> ChatPromptTemplate:

        template_name = name or PromptType.RAG.value
        return ChatPromptTemplate.from_template(
            self.get_prompt(template_name)
        )
    
    def _get_prompt_template(self, name: str) -> str:

        if not self.langfuse:
            logger.warning(f"Langfuse未初始化，返回默认模板: {name}")
            return self.prompts.get(name, "")
            
        try:
            langfuse_prompt = self.langfuse.get_prompt(name)
            template = self.prompts.get(name, "")
            logger.info(f"成功从Langfuse加载模板 '{name}'--》{template}")
            return template

        except Exception as e:
            logger.warning(f"提示词模板{name}从Langfuse获取失败: {str(e)}")
            return self.prompts.get(name, "")

    def get_prompt(self, name: str) -> str:

        if not name:
            logger.warning("提示词模板名称为空")
            return self._default_system_message

        cache_key = f"prompt_{name}"

        def load_prompt():
            logger.info(f"加载模板 '{name}'")
            return self._get_prompt_template(name)

        return self._cache_manager.get_or_create(
            cache_key,
            load_prompt,
            ttl=1800  # 30分钟过期
        )
        
    def clear_cache(self, name: Optional[str] = None) -> None:

        if name:
            self._cache.pop(name, None)
            logger.info(f"已清除模板缓存: {name}")
        else:
            self._cache.clear()
            logger.info("已清除所有模板缓存")


# 全局单例实例
prompt_manager = PromptManager()