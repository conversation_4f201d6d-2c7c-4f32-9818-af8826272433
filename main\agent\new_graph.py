#!/usr/bin/env/ python
# -*- coding: utf-8 -*-
"""
@author: <PERSON><PERSON><PERSON><PERSON>
@file: new_graph.py
@time: 2025/2/7 10:37
@Description: 优化版本 - 并行检索和搜索的智能RAG系统

主要优化特性:
1. 并行执行检索和搜索，提高响应速度
2. 智能置信度评估，自动选择最佳答案
3. 高级缓存管理，减少重复计算
4. 流式输出优化，提升用户体验
5. 性能监控和降级策略
6. 线程池管理，提高并发处理能力

核心改进:
- 使用LangGraph原生并行节点，真正的并行执行检索和搜索
- 智能置信度评估，自动选择最佳答案源
- 支持多种流式输出策略和配置
- 增加了详细的性能监控和错误处理
- 利用LangGraph的状态管理和节点调度优势
"""
import re
import asyncio
import time
from typing import Dict, TypedDict, Any, Optional
from concurrent.futures import ThreadPoolExecutor

from langgraph.graph import StateGraph, START, END
from langgraph.graph.state import CompiledStateGraph
from langgraph.checkpoint.memory import MemorySaver
from langfuse.callback import CallbackHandler

from common.myLog import logger
from core.chain import ChainFactory
from core.prompt import prompt_manager
from core.tools import TavilySearchResultsTool
from const.cache import cache
from common.utils import measure_time
from common.cache_manager import AdvancedCacheManager, global_cache
from config.streaming_config import StreamingConfig, StreamingOptimizer, BALANCED_STREAMING

memory = MemorySaver()
logger.name = __name__

# 全局正则表达式 - 优化匹配模式
ANSWER_PATTERN = re.compile(r"未学习|抱歉|对不起|不知道|无法回答|没有相关信息|无法找到")
CONFIDENCE_PATTERN = re.compile(r"可能|大概|也许|估计|应该是")


class ChatState(TypedDict):
    """优化的对话状态管理"""
    question: str
    answer: str
    search_result: str
    target_collection: str
    needs_search: bool
    confidence_score: float
    parallel_results: Dict[str, Any]


class ParallelWorkflowEngine:
    """并行智能工作流引擎 - 优化版本"""
    
    _cache_manager = global_cache
    # _executor = ThreadPoolExecutor(max_workers=4)  # 线程池用于并行处理

    def __init__(
            self,
            session_state: Dict[str, Any] = None,
            thread_id: str = None
    ) -> None:
        self.session_state = session_state
        self.thread_id = thread_id
        self.chain_factory = ChainFactory()

        try:
            logger.info("开始创建并行工作流引擎...")
            self.router_chain = self._get_router_chain()
            self.workflow = self._build_parallel_workflow()
        except Exception as e:
            logger.error(f"ParallelWorkflowEngine 初始化失败: {str(e)}", exc_info=True)
            raise

    @measure_time
    def _get_router_chain(self):
        """获取路由链（使用高级缓存管理器）"""
        cache_key = f"router_{self.thread_id}"

        def create_router_chain():
            logger.info(f"创建新的路由 chain: {cache_key}")
            return self.chain_factory.create_chain(
                chain_type="router",
                session_state=self.session_state
            )

        return self._cache_manager.get_or_create(
            cache_key,
            create_router_chain,
            ttl=1800
        )

    @measure_time
    def _get_rag_chain(self, collection_name):
        """获取RAG链（使用高级缓存管理器）"""
        cache_key = f"rag_{collection_name}_{self.thread_id}"

        def create_rag_chain():
            logger.info(f"开始创建 RAG chain: {collection_name}")
            chain = self.chain_factory.create_chain(
                chain_type="rag",
                retriever_type="Rerank",
                collection_name=collection_name,
                session_state=self.session_state
            )
            logger.info(f"RAG chain 创建成功: {collection_name}")
            return chain

        return self._cache_manager.get_or_create(
            cache_key,
            create_rag_chain,
            ttl=3600
        )

    @measure_time
    def _get_common_chain(self):
        """获取通用链（使用高级缓存管理器）"""
        cache_key = f"common_{self.thread_id}"

        def create_common_chain():
            logger.info(f"创建新的通用 chain: {cache_key}")
            return self.chain_factory.create_chain(
                chain_type="common",
                prompt=prompt_manager.utils_prompt("search_prompt"),
                session_state=self.session_state
            )

        return self._cache_manager.get_or_create(
            cache_key,
            create_common_chain,
            ttl=1800
        )

    def _calculate_confidence_score(self, answer: str) -> float:
        """计算答案的置信度分数"""
        if ANSWER_PATTERN.search(answer):
            return 0.1  # 明确表示不知道
        
        if CONFIDENCE_PATTERN.search(answer):
            return 0.6  # 不确定的回答
        
        # 基于答案长度和内容质量的简单评分
        if len(answer) < 50:
            return 0.4
        elif len(answer) > 200:
            return 0.9
        else:
            return 0.7



    def _build_parallel_workflow(self) -> CompiledStateGraph:
        """构建真正的并行工作流 - 使用LangGraph原生并行节点"""
        builder = StateGraph(ChatState)

        async def route_question(state: ChatState) -> Dict:
            """路由问题"""
            logger.info(f"路由问题: {state['question']}")
            try:
                answer = await self.router_chain.ainvoke({
                    "question": state["question"]
                })
                cache.put(f"target_collection_{self.thread_id}", answer)
                logger.info(f"路由结果: {answer}")
                return {"target_collection": answer}
            except Exception as e:
                logger.error(f"路由错误: {str(e)}", exc_info=True)
                raise RuntimeError(f"路由处理失败: {str(e)}")

        async def retrieve_answer(state: ChatState) -> Dict:
            """检索节点 - 独立执行"""
            try:
                logger.info("开始检索任务...")
                processor = self._get_rag_chain(state["target_collection"])
                result = await processor.ainvoke({"question": state["question"]})

                # 计算置信度
                confidence_score = self._calculate_confidence_score(result)
                logger.info(f"检索任务完成，置信度: {confidence_score:.2f}")

                return {
                    "answer": result,
                    "confidence_score": confidence_score
                }
            except Exception as e:
                logger.error(f"检索任务失败: {str(e)}")
                return {
                    "answer": "检索服务暂时不可用",
                    "confidence_score": 0.1
                }

        async def search_answer(state: ChatState) -> Dict:
            """搜索节点 - 独立执行"""
            try:
                logger.info("开始搜索任务...")
                loop = asyncio.get_event_loop()
                search_results = await loop.run_in_executor(
                    None,
                    TavilySearchResultsTool,
                    state["question"],
                    5
                )

                common_chain = self._get_common_chain()
                processed_result = await common_chain.ainvoke({
                    "question": state["question"],
                    "answer": search_results
                })
                logger.info("搜索任务完成")

                return {"search_result": processed_result}
            except Exception as e:
                logger.error(f"搜索任务失败: {str(e)}")
                return {"search_result": "搜索服务暂时不可用"}

        def intelligent_decision(state: ChatState) -> Dict:
            """智能决策 - 基于置信度选择最佳答案"""
            confidence_threshold = 0.5

            # 获取检索结果和搜索结果
            retrieve_answer = state.get("answer", "")
            search_result = state.get("search_result", "")
            confidence_score = state.get("confidence_score", 0.0)

            if confidence_score >= confidence_threshold:
                logger.info(f"检索结果置信度高({confidence_score:.2f})，使用检索结果")
                final_answer = retrieve_answer
                needs_search = False
            else:
                logger.info(f"检索结果置信度低({confidence_score:.2f})，使用搜索结果")
                final_answer = f"基于网络搜索的回答：\n{search_result}"
                needs_search = True

            return {
                "answer": final_answer,
                "needs_search": needs_search,
                "parallel_results": {
                    "retrieve": retrieve_answer,
                    "search": search_result,
                    "confidence": confidence_score,
                    "selected": "search" if needs_search else "retrieve"
                }
            }

        # 注册所有节点
        builder.add_node("route", route_question)
        builder.add_node("retrieve", retrieve_answer)
        builder.add_node("search", search_answer)
        builder.add_node("decide", intelligent_decision)

        # 构建真正的并行流程
        builder.add_edge(START, "route")

        # 关键：从route节点同时启动两个并行节点
        builder.add_edge("route", "retrieve")
        builder.add_edge("route", "search")

        # 两个并行节点都完成后，进入决策节点
        builder.add_edge("retrieve", "decide")
        builder.add_edge("search", "decide")

        builder.add_edge("decide", END)

        return builder.compile(checkpointer=memory)

    async def astream_parallel(self, question: str, config: Optional[StreamingConfig] = None):
        """并行优化的流式输出"""
        streaming_config = config or BALANCED_STREAMING
        optimizer = StreamingOptimizer(streaming_config)

        workflow_config = {
            "configurable": {
                "thread_id": self.thread_id,
                "streaming": True
            },
            "callbacks": [CallbackHandler()],
            "metadata": {
                "langfuse_user_id": self.thread_id,
            }
        }

        buffer = []
        target_collection = None
        last_flush_time = time.time()

        async def smart_flush():
            """智能刷新缓冲区"""
            nonlocal last_flush_time
            if buffer:
                content = "".join(buffer)
                buffer.clear()
                last_flush_time = time.time()
                return content
            return ""

        try:
            async for event in self.workflow.astream_events(
                    {"question": question},
                    workflow_config,
                    version=streaming_config.version,
                    stream_mode=streaming_config.stream_mode
            ):
                if target_collection is None and streaming_config.enable_caching:
                    target_collection = cache.get(f"target_collection_{self.thread_id}")

                event_type = event.get("event")

                if event_type not in streaming_config.event_types:
                    continue

                if event_type == "on_chat_model_stream":
                    node_name = event.get('metadata', {}).get('langgraph_node', '')
                    if node_name in streaming_config.target_nodes:
                        chunk_content = event.get("data", {}).get('chunk', {})
                        if hasattr(chunk_content, 'content') and chunk_content.content:
                            content = chunk_content.content
                            buffer.append(content)

                            if streaming_config.enable_metrics:
                                optimizer.update_metrics(len(content), 0.001)

                            if optimizer.should_flush_buffer(len(buffer), last_flush_time):
                                flushed_content = await smart_flush()
                                if flushed_content:
                                    yield flushed_content, target_collection

                elif event_type == "on_chain_end" and event.get('name') == "LangGraph":
                    flushed_content = await smart_flush()
                    if flushed_content:
                        yield flushed_content, target_collection

                    output_data = event.get("data", {}).get('output', {})
                    answer = output_data.get('answer', '')
                    if answer and not buffer:
                        yield answer, target_collection

            final_content = await smart_flush()
            if final_content:
                yield final_content, target_collection

            if streaming_config.enable_metrics:
                metrics = optimizer.get_metrics()
                logger.info(f"并行流式输出统计: {metrics}")

        except Exception as e:
            logger.error(f"并行流式输出错误: {e}", exc_info=True)
            raise

    @classmethod
    def get_cache_stats(cls):
        """获取缓存统计信息"""
        return cls._cache_manager.get_stats()

    @classmethod
    def clear_cache(cls):
        """清理所有缓存"""
        cls._cache_manager.clear()

    async def astream_with_fallback(self, question: str, config: Optional[StreamingConfig] = None):
        """带降级策略的流式输出"""
        max_retries = 3
        retry_delay = 1.0

        for attempt in range(max_retries):
            try:
                async for chunk, collection in self.astream_parallel(question, config):
                    yield chunk, collection
                return
            except Exception as e:
                logger.warning(f"流式输出尝试 {attempt + 1} 失败: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay * (attempt + 1))
                else:
                    # 最后一次尝试失败，使用简单回退策略
                    logger.error("所有流式输出尝试都失败，使用回退策略")
                    yield "抱歉，系统暂时繁忙，请稍后重试。", None

    async def get_parallel_results_summary(self, question: str) -> Dict[str, Any]:
        """获取并行处理结果的详细摘要 - 使用LangGraph执行完整流程"""
        try:
            # 执行完整的工作流
            config = {
                "configurable": {"thread_id": f"{self.thread_id}_summary"}
            }

            result = await self.workflow.ainvoke(
                {"question": question},
                config
            )

            # 从结果中提取详细信息
            parallel_results = result.get("parallel_results", {})

            return {
                "question": question,
                "target_collection": result.get("target_collection", "unknown"),
                "retrieve_result": parallel_results.get("retrieve", ""),
                "search_result": parallel_results.get("search", ""),
                "confidence_score": parallel_results.get("confidence", 0.0),
                "selected_strategy": parallel_results.get("selected", "unknown"),
                "final_answer": result.get("answer", ""),
                "needs_search": result.get("needs_search", False),
                "recommended_answer": result.get("answer", ""),
                "processing_strategy": f"{parallel_results.get('selected', 'unknown')}_priority"
            }
        except Exception as e:
            logger.error(f"获取并行结果摘要失败: {e}")
            return {
                "error": str(e),
                "fallback_message": "系统处理异常，请稍后重试"
            }

    # def __del__(self):
    #     """清理资源"""
    #     if hasattr(self, '_executor'):
    #         self._executor.shutdown(wait=False)


class PerformanceMonitor:
    """性能监控器"""

    def __init__(self):
        self.metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "avg_response_time": 0.0,
            "cache_hit_rate": 0.0,
            "parallel_efficiency": 0.0
        }
        self.response_times = []

    def record_request(self, success: bool, response_time: float):
        """记录请求指标"""
        self.metrics["total_requests"] += 1
        if success:
            self.metrics["successful_requests"] += 1
        else:
            self.metrics["failed_requests"] += 1

        self.response_times.append(response_time)
        self.metrics["avg_response_time"] = sum(self.response_times) / len(self.response_times)

    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return self.metrics.copy()


if __name__ == '__main__':
    workflow = ParallelWorkflowEngine(thread_id="test_parallel")
    monitor = PerformanceMonitor()

    async def test_parallel_workflow(msg):
        """测试并行工作流"""
        print(f"\n=== 测试并行优化工作流 ===")
        start_time = time.time()
        full_response = ""
        chunk_count = 0

        try:
            async for chunk, _ in workflow.astream_parallel(msg):
                if chunk:
                    full_response += chunk
                    chunk_count += 1
                    print(f"收到块 {chunk_count}: {len(chunk)} 字符")

            duration = time.time() - start_time
            monitor.record_request(True, duration)

            print(f"总耗时: {duration:.2f}秒")
            print(f"总字符数: {len(full_response)}")
            print(f"输出速度: {len(full_response) / duration:.2f} 字符/秒")

        except Exception as e:
            duration = time.time() - start_time
            monitor.record_request(False, duration)
            logger.error(f"测试失败: {e}")

        return full_response

    async def test_parallel_summary(msg):
        """测试并行结果摘要"""
        print(f"\n=== 测试并行结果摘要 ===")
        summary = await workflow.get_parallel_results_summary(msg)

        print(f"问题: {summary.get('question', 'N/A')}")
        print(f"目标集合: {summary.get('target_collection', 'N/A')}")
        print(f"置信度分数: {summary.get('confidence_score', 0):.2f}")
        print(f"处理策略: {summary.get('processing_strategy', 'N/A')}")
        print(f"推荐答案长度: {len(summary.get('recommended_answer', ''))}")

        return summary

    async def run_comprehensive_test():
        """运行综合测试"""
        test_questions = [
            "预约单无法关闭",
            "如何优化系统性能",
            "最新的技术趋势是什么"
        ]

        for i, question in enumerate(test_questions, 1):
            print(f"\n{'='*50}")
            print(f"测试 {i}: {question}")
            print(f"{'='*50}")

            # 测试流式输出
            await test_parallel_workflow(question)

            # 测试结果摘要
            await test_parallel_summary(question)

            print(f"\n性能监控: {monitor.get_metrics()}")
            print(f"缓存统计: {ParallelWorkflowEngine.get_cache_stats()}")

    # 运行测试
    asyncio.run(run_comprehensive_test())


"""
使用说明:

1. 基本使用:
   workflow = ParallelWorkflowEngine(thread_id="user_123")

   # 流式输出
   async for chunk, collection in workflow.astream_parallel(question):
       print(chunk, end='', flush=True)

2. 带配置的使用:
   from config.streaming_config import FAST_STREAMING

   async for chunk, collection in workflow.astream_parallel(question, FAST_STREAMING):
       print(chunk, end='', flush=True)

3. 获取详细结果:
   summary = await workflow.get_parallel_results_summary(question)
   print(f"置信度: {summary['confidence_score']}")
   print(f"选择策略: {summary['selected_strategy']}")

4. 性能监控:
   stats = ParallelWorkflowEngine.get_cache_stats()
   print(f"缓存统计: {stats}")

LangGraph原生并行的优势:
- 真正的并行执行：LangGraph引擎级别的并行调度
- 状态管理优化：自动处理节点间的状态传递和合并
- 内存效率：避免手动asyncio.gather的内存开销
- 错误隔离：单个节点失败不影响其他并行节点
- 可视化调试：支持LangGraph的可视化工具
- 更好的监控：内置的执行追踪和性能指标

技术优势对比:
手动并行 vs LangGraph原生并行:
- 手动: asyncio.gather() -> 需要手动管理异常和状态
- 原生: LangGraph调度器 -> 自动优化执行顺序和资源分配
- 手动: 复杂的状态合并逻辑 -> 容易出错
- 原生: 声明式状态管理 -> 更可靠和可维护

配置选项:
- FAST_STREAMING: 快速响应，适合实时对话
- BALANCED_STREAMING: 平衡性能和质量
- DEBUG_STREAMING: 调试模式，详细日志

注意事项:
1. 确保所有依赖项已正确安装
2. 配置好相关的API密钥（Tavily、OpenAI等）
3. 检查数据库连接（Redis、Milvus等）
4. 监控系统资源使用情况
5. 利用LangGraph Studio进行可视化调试
"""
